package cli

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/berrijam/mulberri/internal/config"
)

func TestTrainingConfigValidate(t *testing.T) {
	// Create temporary test files
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n3,4,no\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n  handle_as: integer\n"), 0644); err != nil {
		t.Fatalf("failed to create test YAML: %v", err)
	}

	tests := []struct {
		name    string
		config  TrainingConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid configuration",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        config.DefaultMaxDepth,
				MinSamplesSplit: config.DefaultMinSamples,
				Criterion:       config.DefaultCriterion,
			},
			wantErr: false,
		},
		{
			name: "valid with feature info file",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        5,
				MinSamplesSplit: 10,
				Criterion:       "entropy",
			},
			wantErr: false,
		},
		{
			name: "missing feature info file",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "feature info file is required",
		},
		{
			name: "missing input file",
			config: TrainingConfig{
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "input file path is required",
		},
		{
			name: "empty target column",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "target column is required",
		},
		{
			name: "whitespace target column",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "   ",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "target column is required",
		},
		{
			name: "empty output file",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "output file is required",
		},
		{
			name: "invalid max depth zero",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        0,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "max depth must be positive",
		},
		{
			name: "invalid max depth negative",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        -5,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "max depth must be positive",
		},
		{
			name: "invalid min samples split",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 1,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "min samples split must be >= 2",
		},
		{
			name: "invalid criterion",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "gini",
			},
			wantErr: true,
			errMsg:  "only entropy criterion supported",
		},
		{
			name: "case insensitive criterion",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "ENTROPY",
			},
			wantErr: true,
			errMsg:  "only entropy criterion supported",
		},
		{
			name: "nonexistent feature info file",
			config: TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: "nonexistent.yaml",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "file does not exist",
		},
		{
			name: "nonexistent input file",
			config: TrainingConfig{
				InputFile:       "nonexistent.csv",
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
			},
			wantErr: true,
			errMsg:  "file does not exist",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestPredictionConfigValidate(t *testing.T) {
	// Create temporary test files
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2\n1,2\n3,4\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test model: %v", err)
	}

	tests := []struct {
		name    string
		config  PredictionConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid configuration",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
			},
			wantErr: false,
		},
		{
			name: "valid with verbose",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
				Verbose:    true,
			},
			wantErr: false,
		},
		{
			name: "missing input file",
			config: PredictionConfig{
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "input file path is required",
		},
		{
			name: "missing model file",
			config: PredictionConfig{
				InputFile:  validCSV,
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "model file is required",
		},
		{
			name: "whitespace model file",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  "   ",
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "model file is required",
		},
		{
			name: "missing output file",
			config: PredictionConfig{
				InputFile: validCSV,
				ModelFile: validModel,
			},
			wantErr: true,
			errMsg:  "output file is required",
		},
		{
			name: "whitespace output file",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validModel,
				OutputFile: "   ",
			},
			wantErr: true,
			errMsg:  "output file is required",
		},
		{
			name: "nonexistent model file",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  "nonexistent.dt",
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "file does not exist",
		},
		{
			name: "invalid model file extension",
			config: PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validCSV, // Using CSV instead of .dt
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "model file must have .dt extension",
		},
		{
			name: "invalid input file extension",
			config: PredictionConfig{
				InputFile:  validModel, // Using .dt instead of .csv
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
			},
			wantErr: true,
			errMsg:  "input file must be CSV format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// Benchmark tests for performance validation
func BenchmarkTrainingConfigValidate(b *testing.B) {
	tempDir := b.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")
	os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n"), 0644)
	os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n  handle_as: integer\n"), 0644)

	cfg := TrainingConfig{
		InputFile:       validCSV,
		TargetCol:       "target",
		OutputFile:      "model.dt",
		FeatureInfoFile: validYAML,
		MaxDepth:        config.DefaultMaxDepth,
		MinSamplesSplit: config.DefaultMinSamples,
		Criterion:       config.DefaultCriterion,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = cfg.Validate()
	}
}

func BenchmarkPredictionConfigValidate(b *testing.B) {
	tempDir := b.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")
	os.WriteFile(validCSV, []byte("col1,col2\n1,2\n"), 0644)
	os.WriteFile(validModel, []byte("{}"), 0644)

	cfg := PredictionConfig{
		InputFile:  validCSV,
		ModelFile:  validModel,
		OutputFile: "predictions.csv",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = cfg.Validate()
	}
}
