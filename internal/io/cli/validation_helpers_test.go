package cli

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestValidateFileExistence(t *testing.T) {
	tempDir := t.TempDir()
	validFile := filepath.Join(tempDir, "test.txt")
	dirPath := filepath.Join(tempDir, "testdir")

	// Create test file and directory
	if err := os.WriteFile(validFile, []byte("test content"), 0644); err != nil {
		t.Fatalf("failed to create test file: %v", err)
	}
	if err := os.Mkdir(dirPath, 0755); err != nil {
		t.Fatalf("failed to create test directory: %v", err)
	}

	tests := []struct {
		name     string
		filePath string
		fileType string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid file",
			filePath: validFile,
			fileType: "test file",
			wantErr:  false,
		},
		{
			name:     "empty file path",
			filePath: "",
			fileType: "test file",
			wantErr:  true,
			errMsg:   "test file path is required",
		},
		{
			name:     "whitespace file path",
			filePath: "   ",
			fileType: "test file",
			wantErr:  true,
			errMsg:   "test file path is required",
		},
		{
			name:     "nonexistent file",
			filePath: "nonexistent.txt",
			fileType: "test file",
			wantErr:  true,
			errMsg:   "file does not exist",
		},
		{
			name:     "directory instead of file",
			filePath: dirPath,
			fileType: "test file",
			wantErr:  true,
			errMsg:   "path is a directory, not a file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateFileExistence(tt.filePath, tt.fileType)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestValidateInputFile(t *testing.T) {
	tempDir := t.TempDir()
	validFile := filepath.Join(tempDir, "test.csv")
	invalidExtFile := filepath.Join(tempDir, "test.txt")
	dirPath := filepath.Join(tempDir, "testdir")

	// Create test files and directory
	if err := os.WriteFile(validFile, []byte("col1,col2\n1,2\n"), 0644); err != nil {
		t.Fatalf("failed to create test file: %v", err)
	}
	if err := os.WriteFile(invalidExtFile, []byte("test"), 0644); err != nil {
		t.Fatalf("failed to create test file: %v", err)
	}
	if err := os.Mkdir(dirPath, 0755); err != nil {
		t.Fatalf("failed to create test directory: %v", err)
	}

	tests := []struct {
		name      string
		inputFile string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "valid CSV file",
			inputFile: validFile,
			wantErr:   false,
		},
		{
			name:      "empty input file",
			inputFile: "",
			wantErr:   true,
			errMsg:    "input file path is required",
		},
		{
			name:      "whitespace input file",
			inputFile: "   ",
			wantErr:   true,
			errMsg:    "input file path is required",
		},
		{
			name:      "invalid file extension",
			inputFile: invalidExtFile,
			wantErr:   true,
			errMsg:    "input file must be CSV format",
		},
		{
			name:      "nonexistent file",
			inputFile: "nonexistent.csv",
			wantErr:   true,
			errMsg:    "file does not exist",
		},
		{
			name:      "directory instead of file",
			inputFile: dirPath,
			wantErr:   true,
			errMsg:    "path is a directory, not a file",
		},
		{
			name:      "case insensitive extension",
			inputFile: filepath.Join(tempDir, "test.CSV"),
			wantErr:   true,
			errMsg:    "file does not exist", // File doesn't exist with uppercase
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateInputFile(tt.inputFile)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestValidateModelFile(t *testing.T) {
	tempDir := t.TempDir()
	validModel := filepath.Join(tempDir, "test.dt")
	invalidExtModel := filepath.Join(tempDir, "test.json")
	invalidTxtModel := filepath.Join(tempDir, "model.txt")
	noExtModel := filepath.Join(tempDir, "model")

	// Create test files
	if err := os.WriteFile(validModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test model: %v", err)
	}
	if err := os.WriteFile(invalidExtModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test file: %v", err)
	}
	if err := os.WriteFile(invalidTxtModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test txt file: %v", err)
	}
	if err := os.WriteFile(noExtModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test file without extension: %v", err)
	}

	tests := []struct {
		name      string
		modelFile string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "valid model file",
			modelFile: validModel,
			wantErr:   false,
		},
		{
			name:      "invalid file extension json",
			modelFile: invalidExtModel,
			wantErr:   true,
			errMsg:    "model file must have .dt extension",
		},
		{
			name:      "invalid file extension txt",
			modelFile: filepath.Join(tempDir, "model.txt"),
			wantErr:   true,
			errMsg:    "model file must have .dt extension",
		},
		{
			name:      "no extension",
			modelFile: filepath.Join(tempDir, "model"),
			wantErr:   true,
			errMsg:    "model file must have .dt extension",
		},
		{
			name:      "nonexistent file",
			modelFile: "nonexistent.dt",
			wantErr:   true,
			errMsg:    "file does not exist",
		},
		{
			name:      "case sensitive extension",
			modelFile: filepath.Join(tempDir, "test.DT"),
			wantErr:   true,
			errMsg:    "file does not exist", // File doesn't exist with uppercase
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateModelFile(tt.modelFile)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestValidateYAMLFile(t *testing.T) {
	tempDir := t.TempDir()
	validYAML := filepath.Join(tempDir, "test.yaml")
	validYML := filepath.Join(tempDir, "test.yml")
	invalidExt := filepath.Join(tempDir, "test.json")
	invalidTxtFile := filepath.Join(tempDir, "config.txt")
	noExtFile := filepath.Join(tempDir, "config")

	// Create test files
	for _, file := range []string{validYAML, validYML, invalidExt, invalidTxtFile, noExtFile} {
		if err := os.WriteFile(file, []byte("test: value\n"), 0644); err != nil {
			t.Fatalf("failed to create test file: %v", err)
		}
	}

	tests := []struct {
		name     string
		yamlFile string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid .yaml file",
			yamlFile: validYAML,
			wantErr:  false,
		},
		{
			name:     "valid .yml file",
			yamlFile: validYML,
			wantErr:  false,
		},
		{
			name:     "invalid file extension json",
			yamlFile: invalidExt,
			wantErr:  true,
			errMsg:   "file must be YAML format",
		},
		{
			name:     "invalid file extension txt",
			yamlFile: filepath.Join(tempDir, "config.txt"),
			wantErr:  true,
			errMsg:   "file must be YAML format",
		},
		{
			name:     "no extension",
			yamlFile: filepath.Join(tempDir, "config"),
			wantErr:  true,
			errMsg:   "file must be YAML format",
		},
		{
			name:     "nonexistent file",
			yamlFile: "nonexistent.yaml",
			wantErr:  true,
			errMsg:   "file does not exist",
		},
		{
			name:     "case sensitive yaml extension",
			yamlFile: filepath.Join(tempDir, "test.YAML"),
			wantErr:  true,
			errMsg:   "file does not exist", // File doesn't exist with uppercase
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateYAMLFile(tt.yamlFile)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// Edge case tests
func TestValidationEdgeCases(t *testing.T) {
	tempDir := t.TempDir()

	t.Run("file with multiple dots in name", func(t *testing.T) {
		filename := filepath.Join(tempDir, "test.data.csv")
		if err := os.WriteFile(filename, []byte("test"), 0644); err != nil {
			t.Fatalf("failed to create test file: %v", err)
		}

		err := validateInputFile(filename)
		if err != nil {
			t.Errorf("should accept file with multiple dots: %v", err)
		}
	})

	t.Run("very long valid filename", func(t *testing.T) {
		longName := strings.Repeat("a", 100) + ".csv"
		filename := filepath.Join(tempDir, longName)
		if err := os.WriteFile(filename, []byte("test"), 0644); err != nil {
			t.Fatalf("failed to create test file: %v", err)
		}

		err := validateInputFile(filename)
		if err != nil {
			t.Errorf("should accept long valid filename: %v", err)
		}
	})
}

// Benchmark tests for validation performance
func BenchmarkValidateInputFile(b *testing.B) {
	tempDir := b.TempDir()
	validFile := filepath.Join(tempDir, "test.csv")
	os.WriteFile(validFile, []byte("test"), 0644)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = validateInputFile(validFile)
	}
}

func BenchmarkValidateModelFile(b *testing.B) {
	tempDir := b.TempDir()
	validModel := filepath.Join(tempDir, "test.dt")
	os.WriteFile(validModel, []byte("{}"), 0644)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = validateModelFile(validModel)
	}
}

func BenchmarkValidateYAMLFile(b *testing.B) {
	tempDir := b.TempDir()
	validYAML := filepath.Join(tempDir, "test.yaml")
	os.WriteFile(validYAML, []byte("test: value"), 0644)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = validateYAMLFile(validYAML)
	}
}
