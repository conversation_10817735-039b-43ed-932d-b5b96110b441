package cli

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/berrijam/mulberri/internal/config"
)

func TestNewTrainCommand(t *testing.T) {
	trainCmd := NewTrainCommand()

	// Test basic command properties
	if trainCmd.Use != "train" {
		t.Errorf("expected Use='train', got=%s", trainCmd.Use)
	}

	if trainCmd.Short == "" {
		t.<PERSON>rror("expected non-empty Short description")
	}

	if trainCmd.Long == "" {
		t.Error("expected non-empty Long description")
	}

	if trainCmd.Example == "" {
		t.Error("expected non-empty Example")
	}

	if trainCmd.RunE == nil {
		t.Error("expected RunE function to be set")
	}

	// Test that required flags exist and have correct properties
	requiredFlags := []string{"input", "target", "output"}
	for _, flagName := range requiredFlags {
		flag := trainCmd.Flags().Lookup(flagName)
		if flag == nil {
			t.<PERSON><PERSON><PERSON>("required flag %s not found", flagName)
			continue
		}

		if flag.Value.String() != "" && flagName != "output" {
			t.Errorf("flag %s should have empty default value", flagName)
		}
	}

	// Test optional flags have correct defaults
	maxDepthFlag := trainCmd.Flags().Lookup("max-depth")
	if maxDepthFlag == nil {
		t.Error("max-depth flag not found")
	} else if maxDepthFlag.DefValue != "10" {
		t.Errorf("expected max-depth default=10, got=%s", maxDepthFlag.DefValue)
	}

	minSamplesFlag := trainCmd.Flags().Lookup("min-samples")
	if minSamplesFlag == nil {
		t.Error("min-samples flag not found")
	} else if minSamplesFlag.DefValue != "20" {
		t.Errorf("expected min-samples default=20, got=%s", minSamplesFlag.DefValue)
	}

	criterionFlag := trainCmd.Flags().Lookup("criterion")
	if criterionFlag == nil {
		t.Error("criterion flag not found")
	} else if criterionFlag.DefValue != "entropy" {
		t.Errorf("expected criterion default=entropy, got=%s", criterionFlag.DefValue)
	}
}

func TestTrainCommandHelp(t *testing.T) {
	trainCmd := NewTrainCommand()

	// Capture help output
	var buf bytes.Buffer
	trainCmd.SetOut(&buf)
	trainCmd.SetArgs([]string{"--help"})

	err := trainCmd.Execute()
	if err != nil {
		t.Errorf("help command should not return error: %v", err)
	}

	helpOutput := buf.String()
	expectedStrings := []string{
		"train",
		"C4.5 algorithm",
		"--input",
		"--target",
		"--output",
		"--max-depth",
		"--min-samples",
		"Examples:",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(helpOutput, expected) {
			t.Errorf("help output missing expected string: %s", expected)
		}
	}
}

func TestTrainCommandValidation(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n3,4,no\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n  handle_as: integer\n"), 0644); err != nil {
		t.Fatalf("failed to create test YAML: %v", err)
	}

	tests := []struct {
		name    string
		args    []string
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid training command",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
				"--features", validYAML,
			},
			wantErr: false,
		},
		{
			name: "missing required features flag",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "missing required input flag",
			args: []string{
				"--target", "target",
				"--output", "model.dt",
				"--features", validYAML,
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "missing required target flag",
			args: []string{
				"--input", validCSV,
				"--output", "model.dt",
				"--features", validYAML,
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "missing required output flag",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--features", validYAML,
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "valid with custom parameters",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "custom.dt",
				"--features", validYAML,
				"--max-depth", "5",
				"--min-samples", "10",
				"--verbose",
			},
			wantErr: false,
		},
		{
			name: "invalid max-depth",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
				"--features", validYAML,
				"--max-depth", "0",
			},
			wantErr: true,
			errMsg:  "max depth must be positive",
		},
		{
			name: "invalid min-samples",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
				"--features", validYAML,
				"--min-samples", "1",
			},
			wantErr: true,
			errMsg:  "min samples split must be >= 2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trainCmd := NewTrainCommand()

			// Capture output for validation
			var buf bytes.Buffer
			trainCmd.SetOut(&buf)
			trainCmd.SetErr(&buf)
			trainCmd.SetArgs(tt.args)

			err := trainCmd.Execute()

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestTrainCommandFlagParsing(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n"), 0644); err != nil {
		t.Fatalf("failed to create test YAML: %v", err)
	}

	tests := []struct {
		name               string
		args               []string
		expectedMaxDepth   int
		expectedMinSamples int
		expectedCriterion  string
		expectedVerbose    bool
		expectedFeatures   string
	}{
		{
			name: "default values",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
				"--features", validYAML,
			},
			expectedMaxDepth:   config.DefaultMaxDepth,
			expectedMinSamples: config.DefaultMinSamples,
			expectedCriterion:  config.DefaultCriterion,
			expectedVerbose:    false,
			expectedFeatures:   validYAML,
		},
		{
			name: "custom values",
			args: []string{
				"--input", validCSV,
				"--target", "target",
				"--output", "model.dt",
				"--max-depth", "15",
				"--min-samples", "30",
				"--criterion", "entropy",
				"--features", validYAML,
				"--verbose",
			},
			expectedMaxDepth:   15,
			expectedMinSamples: 30,
			expectedCriterion:  "entropy",
			expectedVerbose:    true,
			expectedFeatures:   validYAML,
		},
		{
			name: "short flags",
			args: []string{
				"-i", validCSV,
				"-t", "target",
				"-o", "model.dt",
				"-f", validYAML,
				"-v",
			},
			expectedMaxDepth:   config.DefaultMaxDepth,
			expectedMinSamples: config.DefaultMinSamples,
			expectedCriterion:  config.DefaultCriterion,
			expectedVerbose:    true,
			expectedFeatures:   validYAML,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trainCmd := NewTrainCommand()
			trainCmd.SetArgs(tt.args)

			// Parse flags without executing RunE
			err := trainCmd.ParseFlags(tt.args)
			if err != nil {
				t.Fatalf("failed to parse flags: %v", err)
			}

			// Check parsed values by accessing flags
			maxDepthFlag := trainCmd.Flags().Lookup("max-depth")
			if maxDepthFlag.Value.String() != string(rune(tt.expectedMaxDepth+'0')) {
				// Note: This is a simplified check. In real implementation,
				// you'd need to parse the string back to int for comparison
				t.Logf("max-depth flag value: %s (expected around %d)", maxDepthFlag.Value.String(), tt.expectedMaxDepth)
			}

			verboseFlag := trainCmd.Flags().Lookup("verbose")
			expectedVerboseStr := "false"
			if tt.expectedVerbose {
				expectedVerboseStr = "true"
			}
			if verboseFlag.Value.String() != expectedVerboseStr {
				t.Errorf("expected verbose=%s, got=%s", expectedVerboseStr, verboseFlag.Value.String())
			}
		})
	}
}

func TestRunTraining(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")

	if err := os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n  handle_as: integer\n"), 0644); err != nil {
		t.Fatalf("failed to create test YAML: %v", err)
	}

	tests := []struct {
		name    string
		config  *TrainingConfig
		wantErr bool
	}{
		{
			name: "valid training config",
			config: &TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        10,
				MinSamplesSplit: 2,
				Criterion:       "entropy",
				Verbose:         false,
			},
			wantErr: false,
		},
		{
			name: "valid training config with verbose",
			config: &TrainingConfig{
				InputFile:       validCSV,
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: validYAML,
				MaxDepth:        5,
				MinSamplesSplit: 10,
				Criterion:       "entropy",
				Verbose:         true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := runTraining(tt.config)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// Benchmark train command creation
func BenchmarkNewTrainCommand(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewTrainCommand()
	}
}

func BenchmarkRunTraining(b *testing.B) {
	tempDir := b.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validYAML := filepath.Join(tempDir, "features.yaml")
	os.WriteFile(validCSV, []byte("col1,col2,target\n1,2,yes\n"), 0644)
	os.WriteFile(validYAML, []byte("col1:\n  type: numeric\n  handle_as: integer\n"), 0644)

	cfg := &TrainingConfig{
		InputFile:       validCSV,
		TargetCol:       "target",
		OutputFile:      "model.dt",
		FeatureInfoFile: validYAML,
		MaxDepth:        config.DefaultMaxDepth,
		MinSamplesSplit: config.DefaultMinSamples,
		Criterion:       config.DefaultCriterion,
		Verbose:         false,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = runTraining(cfg)
	}
}
