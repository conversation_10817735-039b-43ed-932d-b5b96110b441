// Package cli provides command-line interface for Mulberri decision tree toolkit.
//
// <PERSON>les command parsing, flag validation, and configuration management using Cobra.
// Delegates actual training and prediction logic to respective internal packages.
//
// Example:
//
//	rootCmd := cli.NewRootCommand()
//	if err := rootCmd.Execute(); err != nil {
//	    log.Fatal(err)
//	}
package cli

import (
	"fmt"
	"os"

	"github.com/berrijam/mulberri/internal/config"
	"github.com/spf13/cobra"
)

// NewRootCommand creates the main Mulberri CLI command with all subcommands.
//
// Returns a configured cobra.Command that handles version, help, and delegates
// to train/predict subcommands. Includes global error handling and version info.
func NewRootCommand() *cobra.Command {
	rootCmd := &cobra.Command{
		Use:   "mulberri",
		Short: "High-performance C4.5 decision tree toolkit",
		Long: `<PERSON><PERSON><PERSON><PERSON> is a C4.5 decision tree implementation for classification tasks.

It supports training models from CSV data and making predictions with trained models.
Built for simplicity, performance, and interpretability.`,
		Version:      config.Version,
		SilenceUsage: true, // Don't show usage on command errors
	}

	// Add subcommands
	rootCmd.AddCommand(NewTrainCommand())
	rootCmd.AddCommand(NewPredictCommand())

	return rootCmd
}

// Execute runs the root command and handles any errors.
//
// This is the main entry point for the CLI application.
// Exits with code 1 on any command execution errors.
func Execute() {
	rootCmd := NewRootCommand()
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
