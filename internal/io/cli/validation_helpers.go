package cli

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// validateFileExistence validates that a file path is not empty, exists, and is not a directory.
//
// Args:
// - filePath: Path to the file to validate
// - fileType: Description of the file type for error messages (e.g., "input file", "model file")
//
// Returns error if path is empty, file doesn't exist, cannot be accessed, or is a directory.
func validateFileExistence(filePath, fileType string) error {
	if strings.TrimSpace(filePath) == "" {
		return fmt.Errorf("%s path is required", fileType)
	}

	// Check file existence and properties
	info, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", filePath)
	}
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}

	if info.IsDir() {
		return fmt.Errorf("path is a directory, not a file: %s", filePath)
	}

	return nil
}

// validateInputFile validates CSV input file existence and format.
//
// Args:
// - inputFile: Path to input CSV file
//
// Returns error if file doesn't exist, has wrong extension, or is directory.
func validateInputFile(inputFile string) error {
	// Validate file existence
	if err := validateFileExistence(inputFile, "input file"); err != nil {
		return err
	}

	// Validate file extension after confirming it's a file
	ext := strings.ToLower(filepath.Ext(inputFile))
	if ext != ".csv" {
		return fmt.Errorf("input file must be CSV format, got: %s", ext)
	}

	return nil
}

// validateModelFile validates trained model file existence and format.
//
// Args:
// - modelFile: Path to trained model file
//
// Returns error if file doesn't exist or has wrong extension (.dt required).
func validateModelFile(modelFile string) error {

	// Validate file existence
	if err := validateFileExistence(modelFile, "model file"); err != nil {
		return err
	}
	// Validate file extension
	ext := strings.ToLower(filepath.Ext(modelFile))
	if ext != ".dt" {
		return fmt.Errorf("model file must have .dt extension, got: %s", ext)
	}
	return nil
}

// validateYAMLFile validates YAML file existence and format.
//
// Args:
// - yamlFile: Path to YAML file
//
// Returns error if file doesn't exist or has wrong extension (.yaml/.yml required).
func validateYAMLFile(yamlFile string) error {

	// Validate file existence
	if err := validateFileExistence(yamlFile, "YAML file"); err != nil {
		return err
	}
	// Validate file extension
	ext := strings.ToLower(filepath.Ext(yamlFile))
	if ext != ".yaml" && ext != ".yml" {
		return fmt.Errorf("file must be YAML format (.yaml or .yml), got: %s", ext)
	}
	return nil
}
