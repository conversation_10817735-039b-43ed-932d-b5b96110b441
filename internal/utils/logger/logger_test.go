package logger

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"go.uber.org/zap/zapcore"
)

// mockLogger implements Logger interface for testing
type mockLogger struct{}

func (m *mockLogger) Debug(msg string) {}
func (m *mockLogger) Info(msg string)  {}
func (m *mockLogger) Warn(msg string)  {}
func (m *mockLogger) Error(msg string) {}
func (m *mockLogger) Fatal(msg string) {}
func (m *mockLogger) Close()           {}

func TestGetLogConfig(t *testing.T) {
	// Test with default values
	config := getLogConfig()

	if config.LogFolder != DefaultLogFolder {
		t.<PERSON>rf("LogFolder = %q, want %q", config.LogFolder, DefaultLogFolder)
	}
	if config.MaxSize != DefaultMaxSize {
		t.<PERSON>("MaxSize = %d, want %d", config.MaxSize, DefaultMaxSize)
	}
	if config.EnableConsole != DefaultEnableConsole {
		t.E<PERSON>rf("EnableConsole = %v, want %v", config.EnableConsole, DefaultEnableConsole)
	}
	if config.AppName != DefaultAppName {
		t.Errorf("AppName = %q, want %q", config.AppName, DefaultAppName)
	}
	if config.EnableColors != DefaultEnableColors {
		t.Errorf("EnableColors = %v, want %v", config.EnableColors, DefaultEnableColors)
	}

	if config.MaxBackups != 7 {
		t.Errorf("MaxBackups = %d, want %d", config.MaxBackups, 7)
	}
	if config.MaxAge != 7 {
		t.Errorf("MaxAge = %d, want %d", config.MaxAge, 7)
	}
	if config.Verbose != false {
		t.Errorf("Verbose = %v, want %v", config.Verbose, false)
	}
	if config.Operation != "" {
		t.Errorf("Operation = %q, want %q", config.Operation, "")
	}
}

func TestDefaultConfigProvider(t *testing.T) {
	// Test default config provider
	provider := NewDefaultConfigProvider()
	config := provider.GetConfig()
	if config.Verbose {
		t.Errorf("Expected verbose=false by default, got %v", config.Verbose)
	}
}

func TestDefaultEncoderFactory(t *testing.T) {
	factory := NewDefaultEncoderFactory()

	// Test console encoder with colors
	encoder := factory.CreateConsoleEncoder(true)
	if encoder == nil {
		t.Error("CreateConsoleEncoder(true) returned nil")
	}

	// Test console encoder without colors
	encoder = factory.CreateConsoleEncoder(false)
	if encoder == nil {
		t.Error("CreateConsoleEncoder(false) returned nil")
	}

	// Test file encoder
	encoder = factory.CreateFileEncoder()
	if encoder == nil {
		t.Error("CreateFileEncoder() returned nil")
	}
}

func TestDefaultWriterFactory(t *testing.T) {
	factory := NewDefaultWriterFactory()
	config := getLogConfig()
	config.Operation = "test"

	writer := factory.CreateFileWriter(config)
	if writer == nil {
		t.Error("CreateFileWriter() returned nil")
		return
	}

	// Check filename format
	expectedDate := time.Now().Format("2006-01-02")
	expectedFilename := filepath.Join(config.LogFolder, "mulberri-test-"+expectedDate+".log")
	if writer.Filename != expectedFilename {
		t.Errorf("Filename = %q, want %q", writer.Filename, expectedFilename)
	}
}

func TestCreateLumberjackWriter(t *testing.T) {
	config := getLogConfig()
	config.Operation = "unittest"
	config.LogFolder = "test_logs"

	writer := createLumberjackWriter(config)

	expectedDate := time.Now().Format("2006-01-02")
	expectedFilename := filepath.Join("test_logs", "mulberri-unittest-"+expectedDate+".log")

	if writer.Filename != expectedFilename {
		t.Errorf("Filename = %q, want %q", writer.Filename, expectedFilename)
	}
	if writer.MaxSize != int(config.MaxSize) {
		t.Errorf("MaxSize = %d, want %d", writer.MaxSize, int(config.MaxSize))
	}
	if writer.MaxBackups != config.MaxBackups {
		t.Errorf("MaxBackups = %d, want %d", writer.MaxBackups, config.MaxBackups)
	}
	if writer.MaxAge != config.MaxAge {
		t.Errorf("MaxAge = %d, want %d", writer.MaxAge, config.MaxAge)
	}
	if !writer.Compress {
		t.Error("Expected Compress = true")
	}

	// Test fallback operation
	config.Operation = ""
	writer = createLumberjackWriter(config)
	if !strings.Contains(writer.Filename, "mulberri-app-") {
		t.Errorf("Expected fallback operation 'app' in filename, got %q", writer.Filename)
	}
}

func TestCreateZapConfig(t *testing.T) {
	// Test with verbose and colors
	config := getLogConfig()
	config.Verbose = true
	config.EnableColors = true

	zapConfig := createZapConfig(config)
	if zapConfig.Level.Level() != zapcore.DebugLevel {
		t.Errorf("Expected DebugLevel for verbose=true, got %v", zapConfig.Level.Level())
	}
	if zapConfig.Encoding != "console" {
		t.Errorf("Expected console encoding, got %q", zapConfig.Encoding)
	}

	// Test with non-verbose and no colors
	config.Verbose = false
	config.EnableColors = false

	zapConfig = createZapConfig(config)
	if zapConfig.Level.Level() != zapcore.InfoLevel {
		t.Errorf("Expected InfoLevel for verbose=false, got %v", zapConfig.Level.Level())
	}
	if zapConfig.Encoding != "console" {
		t.Errorf("Expected console encoding, got %q", zapConfig.Encoding)
	}
}

func TestLoggerFactory(t *testing.T) {
	configProvider := NewDefaultConfigProvider()
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
	if factory == nil {
		t.Error("NewLoggerFactory returned nil")
	}

	// Create temporary log directory
	tempDir := "test_logs_factory"
	defer os.RemoveAll(tempDir)

	// Update config to use temp directory
	config := configProvider.GetConfig()
	config.LogFolder = tempDir
	config.Operation = "factorytest"
	configProvider = &DefaultConfigProvider{config: config}
	factory = NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	logger, err := factory.CreateLogger()
	if err != nil {
		t.Fatalf("CreateLogger() failed: %v", err)
	}
	if logger == nil {
		t.Error("CreateLogger() returned nil logger")
	}

	logger.Close()
}

func TestNewLoggerWithVerbose(t *testing.T) {
	// Create temporary log directory
	tempDir := "test_logs_verbose"
	defer os.RemoveAll(tempDir)

	// Test default logger
	logger, err := NewLoggerWithVerbose()
	if err != nil {
		t.Fatalf("NewLoggerWithVerbose() failed: %v", err)
	}
	if logger == nil {
		t.Error("NewLoggerWithVerbose() returned nil")
	}
	logger.Close()
	if logger == nil {
		t.Error("NewLoggerWithVerbose(false) returned nil")
	}
	logger.Close()
}

func TestZapLoggerMethods(t *testing.T) {
	// Create temporary log directory
	tempDir := "test_logs_methods"
	defer os.RemoveAll(tempDir)

	config := getLogConfig()
	config.Operation = "methodtest"
	config.Verbose = true
	config.LogFolder = tempDir

	logger, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test all logging methods
	logger.Debug("Debug message")
	logger.Info("Info message")
	logger.Warn("Warn message")
	logger.Error("Error message")

	logger.Close()
}

func TestAutoInitialization(t *testing.T) {
	// Test that logger auto-initializes when first used
	CloseGlobal() // Ensure no global logger exists

	// This should auto-initialize the logger
	Info("Test auto-initialization")

	// Verify logger was created
	_ = IsVerboseEnabled() // Just check it doesn't panic

	CloseGlobal()
}

func TestGlobalLoggerFunctions(t *testing.T) {
	// Test basic global functions
	Debug("Test debug message")
	Info("Test info message")
	Warn("Test warn message")
	Error("Test error message")

	// Test that regular logging functions work after initialization

	CloseGlobal()
}

func TestSetGlobalConfig(t *testing.T) {
	config := getLogConfig()
	config.Operation = "globaltest"
	config.Verbose = true

	err := SetGlobalConfig(config)
	if err != nil {
		t.Errorf("SetGlobalConfig failed: %v", err)
	}

	CloseGlobal()
}

func TestIsVerboseEnabled(t *testing.T) {
	// Test with no global logger
	CloseGlobal()
	globalLogger = nil
	if IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return false when no global logger")
	}

	// Test with logger auto-initialized (uses values from getLogConfig)
	Info("This will auto-initialize the logger")
	// Note: This will return whatever verbose value is set in getLogConfig()
	// Since we can't control it in tests, we just verify the function doesn't panic
	_ = IsVerboseEnabled()

	CloseGlobal()
}

func TestNewWithConfigAndFactories(t *testing.T) {
	tempDir := "test_logs_config_factories"
	defer os.RemoveAll(tempDir)

	config := getLogConfig()
	config.LogFolder = tempDir
	config.Operation = "configtest"
	config.EnableConsole = false // Test without console output

	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	logger, err := NewWithConfigAndFactories(config, encoderFactory, writerFactory)
	if err != nil {
		t.Fatalf("NewWithConfigAndFactories failed: %v", err)
	}

	logger.Info("Test message")
	logger.Close()

	// Verify log file was created
	expectedDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(tempDir, "mulberri-configtest-"+expectedDate+".log")
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		t.Errorf("Log file %s was not created", logFile)
	}
}

func TestErrorCases(t *testing.T) {
	// Test with invalid log folder (permission denied)
	config := getLogConfig()
	config.LogFolder = "/root/invalid_folder" // Should fail on most systems
	config.Operation = "errortest"

	_, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err == nil {
		t.Error("Expected error when creating logger with invalid log folder")
	}
}

func TestZapLoggerClose(t *testing.T) {
	// Test closing logger with nil zapLogger
	logger := &ZapLogger{
		zapLogger: nil,
	}
	logger.Close() // Should not panic

	// Test normal close
	config := getLogConfig()
	config.LogFolder = "test_logs_close"
	config.Operation = "closetest"
	defer os.RemoveAll("test_logs_close")

	logger2, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	logger2.Close() // Should not panic
}

func TestInitGlobalLogger(t *testing.T) {
	// Test that global logger initializes automatically
	CloseGlobal() // Ensure clean state

	// This should trigger auto-initialization
	Info("Test message")

	// Test that global logger was initialized
	if globalLogger == nil {
		t.Error("Expected global logger to be initialized")
	}

	CloseGlobal()
}

func TestGetGlobalLogger(t *testing.T) {
	// Reset global logger completely for clean test state
	resetGlobalForTesting()

	// Test lazy initialization
	logger := getGlobalLogger()
	if logger == nil {
		t.Error("getGlobalLogger() returned nil")
	}

	// Test that subsequent calls return the same logger
	logger2 := getGlobalLogger()
	if logger != logger2 {
		t.Error("getGlobalLogger() should return the same instance")
	}

	CloseGlobal()
}

func TestOperationConstants(t *testing.T) {
	// Test operation constants
	if OperationTrain != "train" {
		t.Errorf("OperationTrain = %q, want %q", OperationTrain, "train")
	}
	if OperationPredict != "predict" {
		t.Errorf("OperationPredict = %q, want %q", OperationPredict, "predict")
	}
}

func TestDefaultConstants(t *testing.T) {
	// Test default constants
	if DefaultLogFolder != "logs" {
		t.Errorf("DefaultLogFolder = %q, want %q", DefaultLogFolder, "logs")
	}
	if DefaultMaxSize != 10 {
		t.Errorf("DefaultMaxSize = %d, want %d", DefaultMaxSize, 10)
	}
	if DefaultEnableConsole != true {
		t.Errorf("DefaultEnableConsole = %v, want %v", DefaultEnableConsole, true)
	}
	if DefaultAppName != "mulberri" {
		t.Errorf("DefaultAppName = %q, want %q", DefaultAppName, "mulberri")
	}
	if DefaultEnableColors != true {
		t.Errorf("DefaultEnableColors = %v, want %v", DefaultEnableColors, true)
	}
}

func TestConfigProviderGetConfig(t *testing.T) {
	config := getLogConfig()
	config.Verbose = true
	config.Operation = "providertest"

	provider := &DefaultConfigProvider{config: config}
	retrievedConfig := provider.GetConfig()

	if retrievedConfig.Verbose != config.Verbose {
		t.Errorf("Retrieved config Verbose = %v, want %v", retrievedConfig.Verbose, config.Verbose)
	}
	if retrievedConfig.Operation != config.Operation {
		t.Errorf("Retrieved config Operation = %q, want %q", retrievedConfig.Operation, config.Operation)
	}
}

func TestIsVerboseEnabledWithNonZapLogger(t *testing.T) {
	// Create a mock logger that doesn't implement ZapLogger
	var mock mockLogger

	// Set global logger to mock
	globalMu.Lock()
	globalLogger = &mock
	globalMu.Unlock()

	// Should return false for non-ZapLogger
	if IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return false for non-ZapLogger")
	}

	CloseGlobal()
}

// resetGlobalForTesting resets the global logger state for testing purposes.
// This function should only be used in tests to ensure clean state between tests.
func resetGlobalForTesting() {
	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}
