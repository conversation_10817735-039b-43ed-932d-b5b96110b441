// Package config provides default configuration values for Mulberri decision tree toolkit.
//
// Centralizes core algorithm parameters and basic settings to ensure consistency
// across training and prediction operations.
package config

const (
	// DefaultMaxDepth limits tree depth to prevent overfitting (must be > 0).
	DefaultMaxDepth = 10

	// DefaultMinSamples sets minimum samples required to create split node (must be >= 2).
	DefaultMinSamples = 20

	// DefaultMinSamplesLeaf sets minimum samples required at leaf node (must be >= 1).
	DefaultMinSamplesLeaf = 1

	// DefaultCriterion specifies impurity measure for C4.5 algorithm.
	DefaultCriterion = "entropy"

	// DefaultMinEntropyGain sets minimum information gain threshold for splits (0.0-1.0).
	DefaultMinEntropyGain = 0.01
)

// Application metadata
const (
	Version   = "0.1.0"
	Algorithm = "C4.5"
)
